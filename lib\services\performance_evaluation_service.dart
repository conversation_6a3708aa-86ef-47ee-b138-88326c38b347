/// خدمة تقييم الأداء
/// توفر وظائف إدارة دورات التقييم ومعايير التقييم وإجراء التقييمات
library;

import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class PerformanceEvaluationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدارة دورات التقييم

  /// الحصول على جميع دورات التقييم
  Future<List<PerformanceCycle>> getAllCycles({
    String? status,
    String? searchQuery,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (status != null && status.isNotEmpty) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (cycle_name LIKE ? OR description LIKE ?)';
        final searchPattern = '%$searchQuery%';
        whereArgs.addAll([searchPattern, searchPattern]);
      }

      final result = await db.query(
        AppConstants.performanceCyclesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'start_date DESC',
      );

      return result.map((map) => PerformanceCycle.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب دورات التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على دورة تقييم بالمعرف
  Future<PerformanceCycle?> getCycleById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.performanceCyclesTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return PerformanceCycle.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب دورة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء دورة تقييم جديدة
  Future<PerformanceCycle> createCycle(PerformanceCycle cycle) async {
    try {
      // التحقق من صحة البيانات
      await _validateCycle(cycle);

      final db = await _databaseHelper.database;

      // التحقق من عدم تداخل التواريخ مع دورات أخرى نشطة
      final overlappingCycles = await _getOverlappingCycles(
        cycle.startDate,
        cycle.endDate,
        excludeId: cycle.id,
      );

      if (overlappingCycles.isNotEmpty) {
        throw ValidationException(
          'يوجد دورة تقييم أخرى نشطة في نفس الفترة الزمنية',
        );
      }

      final now = DateTime.now();
      final cycleData = cycle.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.performanceCyclesTable,
        cycleData.toMap(),
      );

      final newCycle = cycleData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'PerformanceCycle',
        entityId: id,
        description: 'إنشاء دورة تقييم أداء جديدة: ${cycle.cycleName}',
        newValues: newCycle.toMap(),
      );

      LoggingService.info(
        'تم إنشاء دورة تقييم أداء جديدة بنجاح',
        category: 'PerformanceEvaluationService',
        data: {'id': id, 'name': cycle.cycleName},
      );

      return newCycle;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء دورة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'cycle': cycle.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث دورة تقييم
  Future<PerformanceCycle> updateCycle(PerformanceCycle cycle) async {
    try {
      if (cycle.id == null) {
        throw ValidationException('معرف الدورة مطلوب للتحديث');
      }

      // التحقق من وجود الدورة
      final existingCycle = await getCycleById(cycle.id!);
      if (existingCycle == null) {
        throw ValidationException('دورة التقييم غير موجودة');
      }

      // التحقق من صحة البيانات
      await _validateCycle(cycle);

      final db = await _databaseHelper.database;

      final updatedCycle = cycle.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.performanceCyclesTable,
        updatedCycle.toMap(),
        where: 'id = ?',
        whereArgs: [cycle.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'PerformanceCycle',
        entityId: cycle.id!,
        description: 'تحديث دورة تقييم أداء: ${cycle.cycleName}',
        oldValues: existingCycle.toMap(),
        newValues: updatedCycle.toMap(),
      );

      LoggingService.info(
        'تم تحديث دورة التقييم بنجاح',
        category: 'PerformanceEvaluationService',
        data: {'id': cycle.id, 'name': cycle.cycleName},
      );

      return updatedCycle;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث دورة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'cycle': cycle.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف دورة تقييم
  Future<void> deleteCycle(int id) async {
    try {
      final cycle = await getCycleById(id);
      if (cycle == null) {
        throw ValidationException('دورة التقييم غير موجودة');
      }

      // التحقق من عدم وجود تقييمات مرتبطة
      final hasEvaluations = await _cycleHasEvaluations(id);
      if (hasEvaluations) {
        throw ValidationException(
          'لا يمكن حذف الدورة لوجود تقييمات مرتبطة بها',
        );
      }

      final db = await _databaseHelper.database;

      // حذف معايير التقييم المرتبطة أولاً
      await db.delete(
        AppConstants.evaluationCriteriaTable,
        where: 'cycle_id = ?',
        whereArgs: [id],
      );

      // حذف الدورة
      await db.delete(
        AppConstants.performanceCyclesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'PerformanceCycle',
        entityId: id,
        description: 'حذف دورة تقييم أداء: ${cycle.cycleName}',
        oldValues: cycle.toMap(),
      );

      LoggingService.info(
        'تم حذف دورة التقييم بنجاح',
        category: 'PerformanceEvaluationService',
        data: {'id': id, 'name': cycle.cycleName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف دورة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إدارة معايير التقييم

  /// الحصول على معايير التقييم لدورة معينة
  Future<List<EvaluationCriteria>> getCriteriaForCycle(int cycleId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.evaluationCriteriaTable,
        where: 'cycle_id = ?',
        whereArgs: [cycleId],
        orderBy: 'criteria_name ASC',
      );

      return result.map((map) => EvaluationCriteria.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب معايير التقييم',
        category: 'PerformanceEvaluationService',
        data: {'cycle_id': cycleId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// للتوافق مع الشاشات الموجودة
  Future<List<PerformanceCycle>> getAllEvaluationCycles() => getAllCycles();

  /// للتوافق مع الشاشات الموجودة
  Future<List<PerformanceEvaluation>> getEmployeeEvaluations() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.performanceEvaluationsTable,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => PerformanceEvaluation.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب تقييمات الموظفين',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// للتوافق مع الشاشات الموجودة
  Future<List<EvaluationCriteria>> getAllEvaluationCriteria({
    bool? isActive,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      final result = await db.query(
        AppConstants.evaluationCriteriaTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'criteria_name ASC',
      );

      return result.map((map) => EvaluationCriteria.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب جميع معايير التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات التقييم
  Future<Map<String, dynamic>> getEvaluationStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final totalCycles = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.performanceCyclesTable}',
      );

      final activeCycles = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.performanceCyclesTable} WHERE status = "active"',
      );

      final totalEvaluations = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.performanceEvaluationsTable}',
      );

      final completedEvaluations = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.performanceEvaluationsTable} WHERE status = "completed"',
      );

      final totalCount = totalCycles.first['count'] as int;
      final activeCount = activeCycles.first['count'] as int;
      final evaluationsCount = totalEvaluations.first['count'] as int;
      final completedCount = completedEvaluations.first['count'] as int;

      final completionRate = evaluationsCount > 0
          ? (completedCount / evaluationsCount * 100).toStringAsFixed(1)
          : '0.0';

      return {
        'totalCycles': totalCount,
        'activeCycles': activeCount,
        'totalEvaluations': evaluationsCount,
        'completedEvaluations': completedCount,
        'completionRate': completionRate,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return {
        'totalCycles': 0,
        'activeCycles': 0,
        'totalEvaluations': 0,
        'completedEvaluations': 0,
        'completionRate': '0.0',
      };
    }
  }

  /// دوال مساعدة خاصة

  /// التحقق من صحة بيانات الدورة
  Future<void> _validateCycle(PerformanceCycle cycle) async {
    if (cycle.cycleName.trim().isEmpty) {
      throw ValidationException('اسم الدورة مطلوب');
    }

    if (cycle.startDate.isAfter(cycle.endDate)) {
      throw ValidationException('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
    }
  }

  /// الحصول على الدورات المتداخلة
  Future<List<PerformanceCycle>> _getOverlappingCycles(
    DateTime startDate,
    DateTime endDate, {
    int? excludeId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '''
        status = 'active' AND
        ((start_date <= ? AND end_date >= ?) OR
         (start_date <= ? AND end_date >= ?) OR
         (start_date >= ? AND end_date <= ?))
      ''';

      List<dynamic> whereArgs = [
        startDate.toIso8601String(),
        startDate.toIso8601String(),
        endDate.toIso8601String(),
        endDate.toIso8601String(),
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ];

      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }

      final result = await db.query(
        AppConstants.performanceCyclesTable,
        where: whereClause,
        whereArgs: whereArgs,
      );

      return result.map((map) => PerformanceCycle.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// التحقق من وجود تقييمات للدورة
  Future<bool> _cycleHasEvaluations(int cycleId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.performanceEvaluationsTable,
        where: 'cycle_id = ?',
        whereArgs: [cycleId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      return true; // في حالة الخطأ، نفترض وجود تقييمات لمنع الحذف
    }
  }

  /// للتوافق مع الشاشات الموجودة - دوال إضافية
  Future<void> createDefaultCriteria() async {
    // تنفيذ إنشاء معايير افتراضية
  }

  Future<List<dynamic>> getEvaluationDetails(int evaluationId) async {
    // تنفيذ جلب تفاصيل التقييم
    return [];
  }

  Future<PerformanceEvaluation> updateEvaluationStatus({
    required int evaluationId,
    required String status,
    required double overallScore,
    double? overallRating,
    String? strengths,
    String? weaknesses,
    String? developmentAreas,
    String? goals,
    String? comments,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على التقييم الحالي
      final result = await db.query(
        AppConstants.performanceEvaluationsTable,
        where: 'id = ?',
        whereArgs: [evaluationId],
      );

      if (result.isEmpty) {
        throw ValidationException('التقييم غير موجود');
      }

      final currentEvaluation = PerformanceEvaluation.fromMap(result.first);

      // تحديث البيانات
      final updatedEvaluation = currentEvaluation.copyWith(
        status: status,
        totalScore: overallScore,
        managerComments: comments,
        goalsNextPeriod: goals,
        updatedAt: DateTime.now(),
      );

      await db.update(
        AppConstants.performanceEvaluationsTable,
        updatedEvaluation.toMap(),
        where: 'id = ?',
        whereArgs: [evaluationId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'PerformanceEvaluation',
        entityId: evaluationId,
        description: 'تحديث حالة تقييم الأداء إلى: $status',
        oldValues: currentEvaluation.toMap(),
        newValues: updatedEvaluation.toMap(),
      );

      LoggingService.info(
        'تم تحديث حالة التقييم بنجاح',
        category: 'PerformanceEvaluationService',
        data: {'evaluation_id': evaluationId, 'status': status},
      );

      return updatedEvaluation;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'evaluation_id': evaluationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  Future<PerformanceEvaluation> createEmployeeEvaluation({
    required int employeeId,
    required int cycleId,
    int? evaluatorId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود تقييم مسبق للموظف في نفس الدورة
      final existingResult = await db.query(
        AppConstants.performanceEvaluationsTable,
        where: 'employee_id = ? AND cycle_id = ?',
        whereArgs: [employeeId, cycleId],
      );

      if (existingResult.isNotEmpty) {
        throw ValidationException('يوجد تقييم مسبق للموظف في هذه الدورة');
      }

      final now = DateTime.now();
      final evaluation = PerformanceEvaluation(
        cycleId: cycleId,
        employeeId: employeeId,
        evaluatorId: evaluatorId ?? 1, // افتراضي
        totalScore: 0,
        status: 'draft',
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(
        AppConstants.performanceEvaluationsTable,
        evaluation.toMap(),
      );

      final newEvaluation = evaluation.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'PerformanceEvaluation',
        entityId: id,
        description: 'إنشاء تقييم أداء جديد للموظف',
        newValues: newEvaluation.toMap(),
      );

      LoggingService.info(
        'تم إنشاء تقييم أداء جديد بنجاح',
        category: 'PerformanceEvaluationService',
        data: {'id': id, 'employee_id': employeeId, 'cycle_id': cycleId},
      );

      return newEvaluation;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقييم الأداء',
        category: 'PerformanceEvaluationService',
        data: {
          'employee_id': employeeId,
          'cycle_id': cycleId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إضافة تفاصيل التقييم
  Future<void> addEvaluationDetail({
    required int evaluationId,
    required int criteriaId,
    required double score,
    String? comments,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final now = DateTime.now();
      final detail = EvaluationDetail(
        evaluationId: evaluationId,
        criteriaId: criteriaId,
        score: score,
        comments: comments,
        createdAt: now,
        updatedAt: now,
      );

      await db.insert(
        AppConstants.evaluationDetailsTable,
        detail.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      LoggingService.info(
        'تم إضافة تفاصيل التقييم بنجاح',
        category: 'PerformanceEvaluationService',
        data: {
          'evaluation_id': evaluationId,
          'criteria_id': criteriaId,
          'score': score,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة تفاصيل التقييم',
        category: 'PerformanceEvaluationService',
        data: {
          'evaluation_id': evaluationId,
          'criteria_id': criteriaId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }
}
