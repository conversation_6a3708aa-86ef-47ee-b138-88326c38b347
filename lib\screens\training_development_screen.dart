/// شاشة التدريب وتطوير الموظفين
/// توفر واجهة شاملة لإدارة برامج التدريب والتطوير المهني
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/training_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../widgets/add_training_program_dialog.dart';
import '../constants/revolutionary_design_colors.dart';

class TrainingDevelopmentScreen extends StatefulWidget {
  const TrainingDevelopmentScreen({super.key});

  @override
  State<TrainingDevelopmentScreen> createState() =>
      _TrainingDevelopmentScreenState();
}

class _TrainingDevelopmentScreenState extends State<TrainingDevelopmentScreen>
    with TickerProviderStateMixin {
  final TrainingService _trainingService = TrainingService();

  late TabController _tabController;
  List<TrainingProgram> _programs = [];
  // البيانات المحملة من الخدمة
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _trainingService.getAllTrainingPrograms(),
        _trainingService.getTrainingStatistics(),
      ]);

      setState(() {
        _programs = results[0] as List<TrainingProgram>;
        _statistics = results[1] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات التدريب',
        category: 'TrainingDevelopmentScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التدريب وتطوير الموظفين'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'add_program':
                  _showAddProgramDialog();
                  break;
                case 'add_session':
                  _showAddSessionDialog();
                  break;
                case 'export':
                  _exportTrainingData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_program',
                child: Text('إضافة برنامج تدريب'),
              ),
              const PopupMenuItem(
                value: 'add_session',
                child: Text('إضافة جلسة تدريب'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.school), text: 'البرامج'),
            Tab(icon: Icon(Icons.event), text: 'الجلسات'),
            Tab(icon: Icon(Icons.people), text: 'التسجيلات'),
            Tab(icon: Icon(Icons.star), text: 'المهارات'),
            Tab(icon: Icon(Icons.trending_up), text: 'خطط التطوير'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildProgramsTab(),
        _buildSessionsTab(),
        _buildEnrollmentsTab(),
        _buildSkillsTab(),
        _buildDevelopmentPlansTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات التدريب والتطوير',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'برامج التدريب',
              '${_statistics['totalPrograms'] ?? 0}',
              Icons.school,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'الجلسات',
              '${_statistics['totalSessions'] ?? 0}',
              Icons.event,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'التسجيلات',
              '${_statistics['totalEnrollments'] ?? 0}',
              Icons.people,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildStatCard(
              'معدل الإكمال',
              '${_statistics['completionRate'] ?? '0.0'}%',
              Icons.check_circle,
              RevolutionaryColors.warningAmber,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إضافة برنامج',
              Icons.add_circle,
              RevolutionaryColors.successGlow,
              _showAddProgramDialog,
            ),
            _buildActionCard(
              'إضافة جلسة',
              Icons.event_available,
              RevolutionaryColors.infoTurquoise,
              _showAddSessionDialog,
            ),
            _buildActionCard(
              'تسجيل موظف',
              Icons.person_add,
              RevolutionaryColors.warningAmber,
              _showEnrollEmployeeDialog,
            ),
            _buildActionCard(
              'إضافة مهارة',
              Icons.star_border,
              RevolutionaryColors.damascusSky,
              _showAddSkillDialog,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final popularPrograms =
        _statistics['popularPrograms'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أكثر البرامج شعبية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (popularPrograms.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد بيانات كافية',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...popularPrograms.map(
            (program) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.1,
                  ),
                  child: Text(
                    '${program['enrollments']}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text(program['name'] ?? 'برنامج غير معروف'),
                subtitle: Text('${program['enrollments']} تسجيل'),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProgramsTab() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في البرامج...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // يمكن تطبيق البحث هنا لاحقاً
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showAddProgramDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة برنامج'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // قائمة البرامج
        Expanded(
          child: _programs.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد برامج تدريب',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _programs.length,
                  itemBuilder: (context, index) {
                    final program = _programs[index];
                    return _buildProgramCard(program);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildProgramCard(TrainingProgram program) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(
          _getProgramIcon(program.category ?? 'general'),
          color: RevolutionaryColors.damascusSky,
        ),
        title: Text(
          program.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${program.category ?? 'عام'} • ${program.durationHours} ساعة',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  program.description ?? 'لا يوجد وصف',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildProgramDetail(
                        'المدة',
                        '${program.durationHours} ساعة',
                      ),
                    ),
                    Expanded(
                      child: _buildProgramDetail(
                        'التكلفة',
                        '${program.cost.toStringAsFixed(0)} ل.س',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildProgramDetail(
                        'الفئة',
                        program.category ?? 'عام',
                      ),
                    ),
                    Expanded(
                      child: _buildProgramDetail(
                        'التكلفة',
                        '${program.cost.toStringAsFixed(0)} ل.س',
                      ),
                    ),
                  ],
                ),
                if (program.prerequisites != null) ...[
                  const SizedBox(height: 8),
                  _buildProgramDetail(
                    'المتطلبات المسبقة',
                    program.prerequisites!,
                  ),
                ],
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _createSessionForProgram(program),
                      icon: const Icon(Icons.add),
                      label: const Text('إنشاء جلسة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _editProgram(program),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgramDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildSessionsTab() {
    return const Center(child: Text('تبويب الجلسات - قيد التطوير'));
  }

  Widget _buildEnrollmentsTab() {
    return const Center(child: Text('تبويب التسجيلات - قيد التطوير'));
  }

  Widget _buildSkillsTab() {
    return const Center(child: Text('تبويب المهارات - قيد التطوير'));
  }

  Widget _buildDevelopmentPlansTab() {
    return const Center(child: Text('تبويب خطط التطوير - قيد التطوير'));
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // البرامج
        return FloatingActionButton(
          onPressed: _showAddProgramDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 2: // الجلسات
        return FloatingActionButton(
          onPressed: _showAddSessionDialog,
          backgroundColor: RevolutionaryColors.infoTurquoise,
          child: const Icon(Icons.event_available, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال مساعدة
  IconData _getProgramIcon(String category) {
    switch (category.toLowerCase()) {
      case 'technical':
        return Icons.computer;
      case 'management':
        return Icons.business;
      case 'soft_skills':
        return Icons.people;
      case 'safety':
        return Icons.security;
      case 'language':
        return Icons.language;
      default:
        return Icons.school;
    }
  }

  // دوال الأحداث
  void _showAddProgramDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTrainingProgramDialog(
        onSave: (program) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          try {
            await _trainingService.addTrainingProgram(program);

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة برنامج التدريب بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _loadData(); // إعادة تحميل البيانات
            }
          } catch (e) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('خطأ في إضافة البرنامج: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showAddSessionDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة جلسة تدريب - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showEnrollEmployeeDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار تسجيل موظف - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showAddSkillDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة مهارة - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _createSessionForProgram(TrainingProgram program) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إنشاء جلسة للبرنامج: ${program.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _editProgram(TrainingProgram program) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تعديل البرنامج: ${program.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _exportTrainingData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير بيانات التدريب - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
